const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config({ path: require('path').join(__dirname, '../.env') });

const seedDemoUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if demo user already exists
    const existingUser = await User.findOne({ username: 'demo' });
    if (existingUser) {
      console.log('Demo user already exists');
      return;
    }

    // Create demo user
    const demoUser = new User({
      username: 'demo',
      password: 'demo123',
      name: 'Demo User',
      email: '<EMAIL>',
      authType: 'local',
      avatar: '',
      preferences: {
        darkMode: false,
        notifications: {
          email: true,
          push: true
        },
        language: 'en'
      }
    });

    await demoUser.save();
    console.log('Demo user created successfully');
    console.log('Username: demo');
    console.log('Password: demo123');

  } catch (error) {
    console.error('Error seeding demo user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the seeder
if (require.main === module) {
  seedDemoUser();
}

module.exports = seedDemoUser;
